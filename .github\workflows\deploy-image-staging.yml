name: STAGING Deploy Images to GHCR

env:
  DOTNET_VERSION: '6.0.x'

on:
  workflow_dispatch:

jobs:
  push-app-image:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: './apps/api'
    steps:
      - name: 'Checkout GitHub Action'
        uses: actions/checkout@main

      - name: 'Login to GitHub Container Registry'
        uses: docker/login-action@v1
        with:
          registry: ghcr.io
          username: ${{github.actor}}
          password: ${{secrets.GITHUB_TOKEN}}

      - name: 'Build Inventory Image'
        run: |
          docker build . --tag ghcr.io/mendableai/firecrawl-staging:latest
          docker push ghcr.io/mendableai/firecrawl-staging:latest