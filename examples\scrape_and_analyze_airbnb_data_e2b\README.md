# Scrape and Analyze Airbnb Data with Firecrawl and E2B

This example demonstrates how to scrape Airbnb data and analyze it using [Firecrawl](https://www.firecrawl.dev/) and the [Code Interpreter SDK](https://github.com/e2b-dev/code-interpreter) from E2B.

## Prerequisites

- Node.js installed on your machine
- An E2B API key
- A Firecrawl API key
- A Anthropic API key

## Setup & run

### 1. Install dependencies

```
npm install
```

### 2. Set up `.env`

1. Copy `.env.template` to `.env`
2. Get [E2B API key](https://e2b.dev/docs/getting-started/api-key)
3. Get [Firecrawl API key](https://firecrawl.dev)
4. Get [Anthropic API key](https://anthropic.com)

### 3. Run the example

```
npm run start
```