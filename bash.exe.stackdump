Stack trace:
Frame         Function      Args
0007FFFF7F40  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFF7F40, 0007FFFF6E40) msys-2.0.dll+0x1FE8E
0007FFFF7F40  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF8218) msys-2.0.dll+0x67F9
0007FFFF7F40  000210046832 (000210286019, 0007FFFF7DF8, 0007FFFF7F40, 000000000000) msys-2.0.dll+0x6832
0007FFFF7F40  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF7F40  000210068E24 (0007FFFF7F50, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF8220  00021006A225 (0007FFFF7F50, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF9108E0000 ntdll.dll
7FF90F3F0000 KERNEL32.DLL
7FF90E230000 KERNELBASE.dll
7FF90FBB0000 USER32.dll
000210040000 msys-2.0.dll
7FF90D9D0000 win32u.dll
7FF90F3C0000 GDI32.dll
7FF90DA00000 gdi32full.dll
7FF90E0C0000 msvcp_win.dll
7FF90DB40000 ucrtbase.dll
7FF90F570000 advapi32.dll
7FF910220000 msvcrt.dll
7FF90E630000 sechost.dll
7FF90E6E0000 RPCRT4.dll
7FF90CED0000 CRYPTBASE.DLL
7FF90DE10000 bcryptPrimitives.dll
7FF9105D0000 IMM32.DLL
