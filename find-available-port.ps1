# Find Available Port Script
Write-Host "=== Finding Available Port for Firecrawl ===" -ForegroundColor Green

# Get excluded port ranges
Write-Host "Getting Windows reserved port ranges..." -ForegroundColor Yellow
$excludedRanges = @()
try {
    $output = netsh int ipv4 show excludedportrange protocol=tcp
    foreach ($line in $output) {
        if ($line -match '^\s*(\d+)\s+(\d+)') {
            $start = [int]$matches[1]
            $end = [int]$matches[2]
            $excludedRanges += @{Start=$start; End=$end}
        }
    }
} catch {
    Write-Host "Could not get excluded ranges" -ForegroundColor Red
}

# Function to check if port is in excluded range
function Test-PortExcluded {
    param($port)
    foreach ($range in $excludedRanges) {
        if ($port -ge $range.Start -and $port -le $range.End) {
            return $true
        }
    }
    return $false
}

# Function to check if port is in use
function Test-PortInUse {
    param($port)
    $result = netstat -ano | findstr ":$port "
    return $result -ne $null
}

# Test common ports
$testPorts = @(8080, 9000, 5000, 7000, 6000, 4000, 8000, 8888, 9999)

Write-Host "`nTesting common ports:" -ForegroundColor Yellow
foreach ($port in $testPorts) {
    $excluded = Test-PortExcluded $port
    $inUse = Test-PortInUse $port
    
    $status = "Available"
    $color = "Green"
    
    if ($excluded) {
        $status = "Reserved by Windows"
        $color = "Red"
    } elseif ($inUse) {
        $status = "In Use"
        $color = "Yellow"
    }
    
    Write-Host "Port $port : $status" -ForegroundColor $color
}

Write-Host "`nRecommended ports for Firecrawl:" -ForegroundColor Green
$recommendedPorts = $testPorts | Where-Object { 
    -not (Test-PortExcluded $_) -and -not (Test-PortInUse $_) 
}

if ($recommendedPorts.Count -gt 0) {
    foreach ($port in $recommendedPorts[0..2]) {
        Write-Host "PORT=$port" -ForegroundColor Cyan
    }
    
    Write-Host "`nTo use the first recommended port, update your .env file:" -ForegroundColor Yellow
    Write-Host "PORT=$($recommendedPorts[0])" -ForegroundColor White
} else {
    Write-Host "No common ports available. Try ports in range 10000-20000" -ForegroundColor Red
}
