apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:alpine
        command: [ "/bin/sh", "-c" ]  # Run a shell script as entrypoint
        args:
          - |
            if [ -n "$REDIS_PASSWORD" ]; then
              echo "Starting Redis with authentication"
              exec redis-server --bind 0.0.0.0 --requirepass "$REDIS_PASSWORD"
            else
              echo "Starting Redis without authentication"
              exec redis-server --bind 0.0.0.0
            fi
        env:
          - name: REDIS_PASSWORD
            valueFrom:
              secretKeyRef:
                name: firecrawl-secret
                key: REDIS_PASSWORD
---
apiVersion: v1
kind: Service
metadata:
  name: redis
spec:
  selector:
    app: redis
  ports:
    - protocol: TCP
      port: 6379
      targetPort: 6379
