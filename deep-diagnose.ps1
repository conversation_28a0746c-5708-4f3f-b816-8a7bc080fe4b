# Deep Port Diagnosis - Find what changed since yesterday
# Run as Administrator for complete information

Write-Host "=== Deep Port Diagnosis - What Changed? ===" -ForegroundColor Green
Write-Host "Run Time: $(Get-Date)" -ForegroundColor Gray
Write-Host ""

# Check if running as Administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
if (-not $isAdmin) {
    Write-Host "Warning: Not running as Administrator, some information may be incomplete" -ForegroundColor Red
    Write-Host ""
}

Write-Host "1. Check Recent Windows Updates:" -ForegroundColor Yellow
try {
    $recentUpdates = Get-WinEvent -FilterHashtable @{LogName='System'; ID=19,20,21,22; StartTime=(Get-Date).AddDays(-2)} -MaxEvents 10 -ErrorAction SilentlyContinue
    if ($recentUpdates) {
        Write-Host "Recent system events found:" -ForegroundColor Red
        $recentUpdates | ForEach-Object { 
            Write-Host "  $($_.TimeCreated): $($_.LevelDisplayName) - $($_.Message.Substring(0, [Math]::Min(100, $_.Message.Length)))" -ForegroundColor Yellow
        }
    } else {
        Write-Host "No recent critical system events" -ForegroundColor Green
    }
} catch {
    Write-Host "Cannot access Windows Event Log" -ForegroundColor Red
}
Write-Host ""

Write-Host "2. Check Hyper-V Dynamic Port Reservations:" -ForegroundColor Yellow
try {
    $winnatReservations = netsh int ipv4 show excludedportrange protocol=tcp
    Write-Host "Current port exclusions:" -ForegroundColor White
    $winnatReservations | ForEach-Object { 
        if ($_ -match "^\s*(\d+)\s+(\d+)") {
            $start = [int]$matches[1]
            $end = [int]$matches[2]
            if (($start -le 3002 -and $end -ge 3002) -or ($start -le 3200 -and $end -ge 3200)) {
                Write-Host "  CONFLICT: $_ (includes our ports)" -ForegroundColor Red
            } else {
                Write-Host "  $_" -ForegroundColor Gray
            }
        } else {
            Write-Host "  $_" -ForegroundColor White
        }
    }
} catch {
    Write-Host "Cannot get port exclusions (Administrator rights required)" -ForegroundColor Red
}
Write-Host ""

Write-Host "3. Check Windows NAT Service Status:" -ForegroundColor Yellow
try {
    $winnatService = Get-Service -Name "winnat" -ErrorAction SilentlyContinue
    if ($winnatService) {
        Write-Host "WinNAT Service Status: $($winnatService.Status)" -ForegroundColor $(if ($winnatService.Status -eq 'Running') { 'Green' } else { 'Red' })
        Write-Host "WinNAT Start Type: $($winnatService.StartType)" -ForegroundColor White
    } else {
        Write-Host "WinNAT service not found" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Cannot check WinNAT service" -ForegroundColor Red
}
Write-Host ""

Write-Host "4. Check Docker Desktop Recent Changes:" -ForegroundColor Yellow
try {
    # Check Docker Desktop process start time
    $dockerProcesses = Get-Process | Where-Object {$_.ProcessName -like "*docker*"}
    if ($dockerProcesses) {
        Write-Host "Docker processes and their start times:" -ForegroundColor White
        $dockerProcesses | ForEach-Object {
            $startTime = $_.StartTime
            $runningTime = (Get-Date) - $startTime
            Write-Host "  $($_.ProcessName) (PID: $($_.Id)): Started $($startTime), Running for $($runningTime.Hours)h $($runningTime.Minutes)m" -ForegroundColor Cyan
        }
    }
} catch {
    Write-Host "Cannot get Docker process information" -ForegroundColor Red
}
Write-Host ""

Write-Host "5. Check WSL2 Status and Recent Changes:" -ForegroundColor Yellow
try {
    $wslStatus = wsl --status 2>$null
    if ($wslStatus) {
        Write-Host "WSL Status:" -ForegroundColor White
        $wslStatus | ForEach-Object { Write-Host "  $_" -ForegroundColor Cyan }
    }
    
    $wslList = wsl --list --verbose 2>$null
    if ($wslList) {
        Write-Host "WSL Distributions:" -ForegroundColor White
        $wslList | ForEach-Object { Write-Host "  $_" -ForegroundColor Cyan }
    }
} catch {
    Write-Host "WSL not available or not configured" -ForegroundColor Yellow
}
Write-Host ""

Write-Host "6. Check Recent Network Configuration Changes:" -ForegroundColor Yellow
try {
    # Check for recent network adapter changes
    $networkEvents = Get-WinEvent -FilterHashtable @{LogName='System'; ProviderName='Microsoft-Windows-Kernel-General'; StartTime=(Get-Date).AddDays(-1)} -MaxEvents 5 -ErrorAction SilentlyContinue
    if ($networkEvents) {
        Write-Host "Recent network events:" -ForegroundColor White
        $networkEvents | ForEach-Object {
            Write-Host "  $($_.TimeCreated): $($_.Message.Substring(0, [Math]::Min(80, $_.Message.Length)))" -ForegroundColor Cyan
        }
    }
} catch {
    Write-Host "Cannot access network event logs" -ForegroundColor Red
}
Write-Host ""

Write-Host "7. Test Port Availability with Different Methods:" -ForegroundColor Yellow
$testPorts = @(3002, 3200, 8080, 9000)
foreach ($port in $testPorts) {
    try {
        $tcpClient = New-Object System.Net.Sockets.TcpClient
        $result = $tcpClient.BeginConnect("127.0.0.1", $port, $null, $null)
        $wait = $result.AsyncWaitHandle.WaitOne(1000, $false)
        
        if ($wait) {
            Write-Host "  Port $port: IN USE" -ForegroundColor Red
            $tcpClient.Close()
        } else {
            Write-Host "  Port $port: AVAILABLE" -ForegroundColor Green
        }
        $tcpClient.Close()
    } catch {
        Write-Host "  Port $port: AVAILABLE (connection refused)" -ForegroundColor Green
    }
}
Write-Host ""

Write-Host "8. Check System Restart Time:" -ForegroundColor Yellow
try {
    $bootTime = (Get-CimInstance -ClassName Win32_OperatingSystem).LastBootUpTime
    $uptime = (Get-Date) - $bootTime
    Write-Host "System last boot: $bootTime" -ForegroundColor White
    Write-Host "System uptime: $($uptime.Days) days, $($uptime.Hours) hours, $($uptime.Minutes) minutes" -ForegroundColor White
    
    if ($uptime.TotalHours -lt 24) {
        Write-Host "System was restarted recently - this might explain the port changes" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Cannot get system boot time" -ForegroundColor Red
}
Write-Host ""

Write-Host "=== Potential Solutions ===" -ForegroundColor Green
Write-Host ""
Write-Host "Based on the diagnosis above, try these solutions in order:" -ForegroundColor Yellow
Write-Host "1. Reset WinNAT service: 'net stop winnat && net start winnat'" -ForegroundColor White
Write-Host "2. Restart Docker Desktop completely" -ForegroundColor White
Write-Host "3. Use a high port number (8080, 9000) that's definitely not reserved" -ForegroundColor White
Write-Host "4. If WSL2 is involved, restart WSL: 'wsl --shutdown'" -ForegroundColor White
Write-Host ""
