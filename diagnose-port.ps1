# Port Diagnostic Script - Check Firecrawl Port 3002 Issues
# Run as Administrator for complete information

Write-Host "=== Firecrawl Port 3002 Diagnostic Report ===" -ForegroundColor Green
Write-Host "Run Time: $(Get-Date)" -ForegroundColor Gray
Write-Host ""

# Check if running as Administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
if (-not $isAdmin) {
    Write-Host "Warning: Not running as Administrator, some information may be incomplete" -ForegroundColor Red
    Write-Host ""
}

Write-Host "1. Check Port 3002 Usage:" -ForegroundColor Yellow
$port3002 = netstat -ano | findstr :3002
if ($port3002) {
    Write-Host $port3002 -ForegroundColor Red
    # Extract PID and show process information
    $pids = $port3002 | ForEach-Object { ($_ -split '\s+')[-1] } | Sort-Object -Unique
    foreach ($processId in $pids) {
        if ($processId -match '^\d+$') {
            try {
                $process = Get-Process -Id $processId -ErrorAction SilentlyContinue
                if ($process) {
                    Write-Host "  Process: $($process.ProcessName) (PID: $processId)" -ForegroundColor Red
                }
            } catch {
                Write-Host "  Cannot get process info for PID $processId" -ForegroundColor Red
            }
        }
    }
} else {
    Write-Host "Port 3002 is currently not in use" -ForegroundColor Green
}
Write-Host ""

Write-Host "2. Check Port Range 3000-3010 Usage:" -ForegroundColor Yellow
$portsRange = netstat -ano | findstr :300
if ($portsRange) {
    $portsRange | ForEach-Object { Write-Host $_ -ForegroundColor Cyan }
} else {
    Write-Host "No ports in use in range 3000-3010" -ForegroundColor Green
}
Write-Host ""

Write-Host "3. Check Windows Reserved Port Ranges:" -ForegroundColor Yellow
try {
    $excludedPorts = netsh int ipv4 show excludedportrange protocol=tcp
    $excludedPorts | ForEach-Object {
        if ($_ -match "3002|300[0-9]") {
            Write-Host $_ -ForegroundColor Red
        } elseif ($_ -match "^\s*\d+\s+\d+") {
            Write-Host $_ -ForegroundColor Gray
        } else {
            Write-Host $_ -ForegroundColor White
        }
    }
} catch {
    Write-Host "Cannot get port reservation info (Administrator rights required)" -ForegroundColor Red
}
Write-Host ""

Write-Host "4. Check Dynamic Port Range:" -ForegroundColor Yellow
try {
    $dynamicPorts = netsh int ipv4 show dynamicport tcp
    $dynamicPorts | ForEach-Object { Write-Host $_ -ForegroundColor White }
} catch {
    Write-Host "Cannot get dynamic port info (Administrator rights required)" -ForegroundColor Red
}
Write-Host ""

Write-Host "5. Check Docker Related Processes:" -ForegroundColor Yellow
$dockerProcesses = Get-Process | Where-Object {$_.ProcessName -like "*docker*"}
if ($dockerProcesses) {
    $dockerProcesses | Format-Table ProcessName, Id, CPU, WorkingSet -AutoSize | Out-String | Write-Host -ForegroundColor Cyan
} else {
    Write-Host "No Docker related processes found" -ForegroundColor Yellow
}

Write-Host "6. Check Docker Service Status:" -ForegroundColor Yellow
$dockerServices = Get-Service | Where-Object {$_.Name -like "*docker*"}
if ($dockerServices) {
    $dockerServices | Format-Table Name, Status, StartType -AutoSize | Out-String | Write-Host -ForegroundColor Cyan
} else {
    Write-Host "No Docker related services found" -ForegroundColor Yellow
}

Write-Host "7. Check Docker Container Status:" -ForegroundColor Yellow
try {
    $containers = docker ps -a 2>$null
    if ($containers) {
        Write-Host $containers -ForegroundColor Cyan
    } else {
        Write-Host "No Docker containers or Docker not running" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Cannot execute docker command" -ForegroundColor Red
}
Write-Host ""

Write-Host "8. Check Hyper-V Related Services:" -ForegroundColor Yellow
$hyperVServices = Get-Service | Where-Object {$_.Name -like "*hyper*" -or $_.Name -like "*vmms*"}
if ($hyperVServices) {
    $hyperVServices | Format-Table Name, Status, StartType -AutoSize | Out-String | Write-Host -ForegroundColor Cyan
} else {
    Write-Host "No Hyper-V related services found" -ForegroundColor Yellow
}

Write-Host "9. Network Adapter Information:" -ForegroundColor Yellow
try {
    $adapters = Get-NetAdapter | Where-Object {$_.Name -like "*docker*" -or $_.Name -like "*hyper*" -or $_.Name -like "*vEthernet*"}
    if ($adapters) {
        $adapters | Format-Table Name, InterfaceDescription, Status -AutoSize | Out-String | Write-Host -ForegroundColor Cyan
    } else {
        Write-Host "No Docker/Hyper-V network adapters found" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Cannot get network adapter information" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Diagnosis Complete ===" -ForegroundColor Green
Write-Host ""
Write-Host "Suggested Solutions:" -ForegroundColor Yellow
Write-Host "1. If port 3002 is reserved, modify docker-compose.yml to use another port" -ForegroundColor White
Write-Host "2. Restart Docker Desktop with Administrator privileges" -ForegroundColor White
Write-Host "3. If problem persists, try restarting Windows network services" -ForegroundColor White
Write-Host ""
