# Pick your baseUrl here:
# @baseUrl = http://localhost:3002
@baseUrl = https://api.firecrawl.dev

### Scrape Website
# @name scrape
POST {{baseUrl}}/v1/scrape HTTP/1.1
Authorization: Bearer {{$dotenv TEST_API_KEY}}
content-type: application/json

{
  "url": "https://firecrawl.dev"
}

### Crawl Website
# @name crawl
POST {{baseUrl}}/v1/crawl HTTP/1.1
Authorization: Bearer {{$dotenv TEST_API_KEY}}
content-type: application/json

{
  "url":"https://firecrawl.dev"
}

### Check Crawl Status
@crawlId = {{crawl.response.body.$.id}}
# @name crawlStatus
GET {{baseUrl}}/v1/crawl/{{crawlId}} HTTP/1.1
Authorization: Bearer {{$dotenv TEST_API_KEY}}

### Cancel Crawl
@crawlId = {{crawl.response.body.$.id}}
# @name cancelCrawl
DELETE {{baseUrl}}/v1/crawl/{{crawlId}} HTTP/1.1
Authorization: Bearer {{$dotenv TEST_API_KEY}}

### Extract website
# @name extract
POST {{baseUrl}}/v1/extract HTTP/1.1
Authorization: Bearer {{$dotenv TEST_API_KEY}}
content-type: application/json

{
  "urls": ["https://firecrawl.dev"],
  "schema": {
    "type": "object",
    "properties": {
      "companyName": {
        "type": "string"
      },
      "companyDescription": {
        "type": "string"
      }
    }
  },
  "agent": {
    "model": "fire-1"
  },
  "origin": "api-sdk"
}

### Check Extract Status
@extractId = {{extract.response.body.$.id}}
# @name extractStatus
GET {{baseUrl}}/v1/extract/{{extractId}} HTTP/1.1
Authorization: Bearer {{$dotenv TEST_API_KEY}}

### Batch Scrape Websites
# @name batchScrape
POST {{baseUrl}}/v1/batch/scrape HTTP/1.1
Authorization: Bearer {{$dotenv TEST_API_KEY}}
content-type: application/json

{
  "urls": [
    "firecrawl.dev",
    "mendable.ai"
  ]
}

### Check Batch Scrape Status
@batchScrapeId = {{batchScrape.response.body.$.id}}
# @name batchScrapeStatus
GET {{baseUrl}}/v1/crawl/{{batchScrapeId}} HTTP/1.1
Authorization: Bearer {{$dotenv TEST_API_KEY}}

### Map Website
# @name map
POST {{baseUrl}}/v1/map HTTP/1.1
Authorization: Bearer {{$dotenv TEST_API_KEY}}
content-type: application/json

{
  "url": "firecrawl.dev",
  "sitemapOnly": true
}

### Generate LLMs TXT
# @name generateLlmsTxt
POST {{baseUrl}}/v1/llmstxt HTTP/1.1
Authorization: Bearer {{$dotenv TEST_API_KEY}}
content-type: application/json

{
  "url": "https://firecrawl.dev",
  "maxUrls": 1,
  "showFullText": false,
  "cache": true
}


### Check Generate LLMs TXT Status
@generateLlmsTxtId = {{generateLlmsTxt.response.body.$.id}}
# @name generateLlmsTxtStatus
GET {{baseUrl}}/v1/llmstxt/{{generateLlmsTxtId}} HTTP/1.1
Authorization: Bearer {{$dotenv TEST_API_KEY}}


### Search
# @name search
POST {{baseUrl}}/v1/search HTTP/1.1
Authorization: Bearer {{$dotenv TEST_API_KEY}}
content-type: application/json

{
  "query": "firecrawl",
  "limit": 50
}
