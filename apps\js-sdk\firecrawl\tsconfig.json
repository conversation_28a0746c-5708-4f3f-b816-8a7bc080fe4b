{
  "compilerOptions": {
    // See https://www.totaltypescript.com/tsconfig-cheat-sheet
    /* Base Options: */
    "esModuleInterop": true,
    "skipLibCheck": true,
    "target": "es2022",
    "allowJs": true,
    "resolveJsonModule": true,
    "moduleDetection": "force",
    "isolatedModules": true,
    "verbatimModuleSyntax": true,

    /* Strictness */
    "strict": true,
    "noUncheckedIndexedAccess": true,
    "noImplicitOverride": true,

    /* If NOT transpiling with TypeScript: */
    "module": "NodeNext",
    "noEmit": true,
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist", "**/__tests__/*"]
}
